const { spawn } = require('child_process');
const fs = require('fs');

console.log('🚨 FINAL FIX - Solving Both Issues');
console.log('==================================');

// Your real credentials
const CLIENT_ID = '58596b7af59c1ddb6ca32f4420c96c28';
const CLIENT_SECRET = 'e1f1cf66f2591d472a0d4b506c210990';
const TUNNEL_URL = 'https://malaysia-finest-bent-versions.trycloudflare.com';

console.log('✅ Using Real Credentials:');
console.log(`   Client ID: ${CLIENT_ID}`);
console.log(`   Tunnel URL: ${TUNNEL_URL}`);

// Fix 1: Update .env with correct credentials
const envContent = `# FINAL FIX - Real Shopify Credentials
SHOPIFY_API_KEY=${CLIENT_ID}
SHOPIFY_API_SECRET=${CLIENT_SECRET}
SHOPIFY_APP_URL=${TUNNEL_URL}
SCOPES=read_orders,write_products,read_customers

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=development
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_development_only

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=${TUNNEL_URL}

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

fs.writeFileSync('.env', envContent);
console.log('✅ Fixed .env file with real credentials');

// Fix 2: Update shopify.app.toml
const tomlContent = `# FINAL FIX - Real Configuration
client_id = "${CLIENT_ID}"
name = "rushrr-courier-app-final"
handle = "rushrr-courier-app-final"
application_url = "${TUNNEL_URL}"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

[access_scopes]
scopes = "read_orders,write_products,read_customers"

[auth]
redirect_urls = ["${TUNNEL_URL}/auth/callback", "${TUNNEL_URL}/auth/shopify/callback", "${TUNNEL_URL}/api/auth/callback"]
`;

fs.writeFileSync('shopify.app.toml', tomlContent);
console.log('✅ Fixed shopify.app.toml with real credentials');

console.log('');
console.log('🎯 PARTNERS DASHBOARD UPDATE REQUIRED:');
console.log('=====================================');
console.log('Go to: https://partners.shopify.com/');
console.log('Find app: shop2423523');
console.log('');
console.log('App URL:');
console.log(TUNNEL_URL);
console.log('');
console.log('Allowed redirection URLs (replace ALL):');
console.log(`${TUNNEL_URL}/auth/callback`);
console.log(`${TUNNEL_URL}/auth/shopify/callback`);
console.log(`${TUNNEL_URL}/api/auth/callback`);
console.log('');

// Start server with explicit environment variables
console.log('🚀 Starting server with REAL credentials...');

const env = {
    ...process.env,
    SHOPIFY_API_KEY: CLIENT_ID,
    SHOPIFY_API_SECRET: CLIENT_SECRET,
    SHOPIFY_APP_URL: TUNNEL_URL,
    SCOPES: 'read_orders,write_products,read_customers',
    PORT: '3000',
    NODE_ENV: 'development'
};

const server = spawn('npm', ['start'], {
    env: env,
    stdio: 'inherit',
    shell: true
});

console.log('');
console.log('🎉 INSTALLATION URL (use after updating Partners Dashboard):');
console.log('===========================================================');
console.log(`https://stoee3923u.myshopify.com/admin/oauth/authorize?client_id=${CLIENT_ID}&scope=read_orders,write_products,read_customers&redirect_uri=${encodeURIComponent(TUNNEL_URL + '/auth/callback')}`);
console.log('');

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down...');
    server.kill();
    process.exit();
});
