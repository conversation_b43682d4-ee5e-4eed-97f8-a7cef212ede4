# 🚀 Rushrr Courier App - Deployment Instructions for stoee3923u.myshopify.com

## ✅ Current Status
Your Remix app has been **built successfully** and is ready for deployment to your Shopify store:
- **Store**: stoee3923u.myshopify.com
- **Password**: leotru

## 🎯 Deployment Steps

### Step 1: Create a Shopify App in Partners Dashboard

1. **Go to Shopify Partners Dashboard**:
   - Visit: https://partners.shopify.com/
   - Log in with your Shopify Partners account
   - If you don't have a Partners account, create one at https://partners.shopify.com/signup

2. **Create a New App**:
   - Click "Apps" in the left sidebar
   - Click "Create app"
   - Choose "Create app manually"
   - Fill in the details:
     - **App name**: Rushrr Courier App
     - **App URL**: `http://localhost:3000` (we'll update this later)
     - **Allowed redirection URLs**: 
       ```
       http://localhost:3000/auth/callback
       http://localhost:3000/auth/shopify/callback
       http://localhost:3000/api/auth/callback
       ```

3. **Configure App Settings**:
   - **App setup**: 
     - Embedded app: ✅ Yes
     - App bridge version: 4.0
   - **App scopes**: 
     - `read_orders`
     - `write_products` 
     - `read_customers`

4. **Get Your App Credentials**:
   - After creating the app, note down:
     - **Client ID** (API key)
     - **Client Secret** (API secret key)

### Step 2: Update App Configuration

1. **Update Environment Variables**:
   Edit the `.env` file in your project:
   ```env
   SHOPIFY_API_KEY=YOUR_CLIENT_ID_HERE
   SHOPIFY_API_SECRET=YOUR_CLIENT_SECRET_HERE
   SHOPIFY_APP_URL=http://localhost:3000
   SCOPES=read_orders,write_products,read_customers
   ```

2. **Update shopify.app.toml**:
   ```toml
   client_id = "YOUR_CLIENT_ID_HERE"
   name = "rushrr-courier-app"
   handle = "rushrr-courier-app"
   application_url = "http://localhost:3000"
   ```

### Step 3: Start the Development Server

1. **Install Dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Build the App**:
   ```bash
   npm run build
   ```

3. **Start the Server**:
   ```bash
   npm start
   ```

### Step 4: Create a Public Tunnel (Required for Shopify)

Since Shopify requires HTTPS and a public URL, you need to create a tunnel:

**Option A: Using ngrok**
```bash
# Install ngrok if you haven't already
npm install -g ngrok

# Create tunnel
ngrok http 3000
```

**Option B: Using Cloudflare Tunnel**
```bash
# Install cloudflared
# Download from: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/

# Create tunnel
cloudflared tunnel --url http://localhost:3000
```

### Step 5: Update App URLs

1. **Copy the tunnel URL** (e.g., `https://abc123.ngrok.io`)

2. **Update Shopify Partners Dashboard**:
   - Go to your app in Partners Dashboard
   - Update **App URL** to your tunnel URL
   - Update **Allowed redirection URLs** to:
     ```
     https://your-tunnel-url.com/auth/callback
     https://your-tunnel-url.com/auth/shopify/callback
     https://your-tunnel-url.com/api/auth/callback
     ```

3. **Update Environment Variables**:
   ```env
   SHOPIFY_APP_URL=https://your-tunnel-url.com
   ```

4. **Restart your server** after updating the environment variables

### Step 6: Install the App

1. **Generate Installation URL**:
   ```
   https://stoee3923u.myshopify.com/admin/oauth/authorize?client_id=YOUR_CLIENT_ID&scope=read_orders,write_products,read_customers&redirect_uri=https://your-tunnel-url.com/auth/callback
   ```

2. **Install the App**:
   - Open the installation URL in your browser
   - Log in to your Shopify store (stoee3923u.myshopify.com) with password: leotru
   - Authorize the app permissions
   - The app will be installed and redirect you to the app interface

## 🔧 Alternative: Using Shopify CLI (Interactive)

If you prefer using Shopify CLI interactively:

1. **Open Terminal/Command Prompt**
2. **Navigate to your project**:
   ```bash
   cd c:\Users\<USER>\Downloads\shopify\shopify-app
   ```
3. **Run Shopify CLI**:
   ```bash
   shopify app dev
   ```
4. **Follow the interactive prompts** to:
   - Create a new app or select existing
   - Choose your store (stoee3923u.myshopify.com)
   - Configure the app settings

## 🧪 Testing Your App

Once installed:
1. Go to your Shopify admin: https://stoee3923u.myshopify.com/admin
2. Navigate to Apps section
3. Click on "Rushrr Courier App"
4. Test the app functionality

## 🚨 Important Notes

- **Keep your tunnel running** while testing the app
- **HTTPS is required** for Shopify apps in production
- **Keep your client secret secure** - never expose it in client-side code
- The app is currently configured for development - for production, you'll need a permanent domain

## 🆘 Troubleshooting

### "Redirect URI not whitelisted" Error
- Ensure your tunnel URL is correctly configured in Partners Dashboard
- Check that redirect URLs match exactly (including https://)

### App Not Loading
- Verify your tunnel is running and accessible
- Check that your server is running on port 3000
- Ensure environment variables are set correctly

### Database Issues
```bash
npx prisma generate
npx prisma db push
```

---

**Your app is ready for deployment! Follow these steps to get it running on your store.** 🎉
