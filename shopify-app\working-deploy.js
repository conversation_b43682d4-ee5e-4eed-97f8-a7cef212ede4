const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Working Deployment...');

// Kill any existing processes
console.log('🔄 Cleaning up existing processes...');

// Update .env with correct values
const envContent = `# Shopify App Configuration - Working Deploy
SHOPIFY_API_KEY=58596b7af59c1ddb6ca32f4420c96c28
SHOPIFY_API_SECRET=e1f1cf66f2591d472a0d4b506c210990
SHOPIFY_APP_URL=https://malaysia-finest-bent-versions.trycloudflare.com
SCOPES=read_orders,write_products,read_customers

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=development
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_development_only

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=https://malaysia-finest-bent-versions.trycloudflare.com

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

fs.writeFileSync('.env', envContent);
console.log('✅ Updated .env file with correct credentials');

// Start the server with environment variables explicitly set
console.log('🚀 Starting server with correct environment...');

const env = {
    ...process.env,
    SHOPIFY_API_KEY: '58596b7af59c1ddb6ca32f4420c96c28',
    SHOPIFY_API_SECRET: 'e1f1cf66f2591d472a0d4b506c210990',
    SHOPIFY_APP_URL: 'https://malaysia-finest-bent-versions.trycloudflare.com',
    SCOPES: 'read_orders,write_products,read_customers',
    PORT: '3000',
    NODE_ENV: 'development'
};

const server = spawn('npm', ['start'], {
    env: env,
    stdio: 'inherit',
    shell: true
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

server.on('close', (code) => {
    console.log(`🔄 Server process exited with code ${code}`);
});

console.log('✅ Server started with correct environment variables!');
console.log('🌐 App URL: https://malaysia-finest-bent-versions.trycloudflare.com');
console.log('🏪 Store: stoee3923u.myshopify.com');
console.log('🔑 Client ID: 58596b7af59c1ddb6ca32f4420c96c28');

console.log('\n🎯 Installation URL:');
console.log('https://stoee3923u.myshopify.com/admin/oauth/authorize?client_id=58596b7af59c1ddb6ca32f4420c96c28&scope=read_orders,write_products,read_customers&redirect_uri=https://malaysia-finest-bent-versions.trycloudflare.com/auth/callback');

// Keep the script running
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down...');
    server.kill();
    process.exit();
});
