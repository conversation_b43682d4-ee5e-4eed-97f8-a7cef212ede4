@echo off
echo 🚀 Starting server with REAL credentials...
echo =====================================

set SHOPIFY_API_KEY=58596b7af59c1ddb6ca32f4420c96c28
set SHOPIFY_API_SECRET=e1f1cf66f2591d472a0d4b506c210990
set SHOPIFY_APP_URL=https://malaysia-finest-bent-versions.trycloudflare.com
set SCOPES=read_orders,write_products,read_customers
set PORT=3000
set NODE_ENV=development

echo ✅ Environment variables set:
echo    SHOPIFY_API_KEY=%SHOPIFY_API_KEY%
echo    SHOPIFY_APP_URL=%SHOPIFY_APP_URL%
echo.

echo 🎯 UPDATE YOUR PARTNERS DASHBOARD:
echo ================================
echo Go to: https://partners.shopify.com/
echo Find app: shop2423523
echo.
echo App URL:
echo %SHOPIFY_APP_URL%
echo.
echo Allowed redirection URLs (replace ALL):
echo %SHOPIFY_APP_URL%/auth/callback
echo %SHOPIFY_APP_URL%/auth/shopify/callback
echo %SHOPIFY_APP_URL%/api/auth/callback
echo.

echo 🚀 Starting server...
npm start
