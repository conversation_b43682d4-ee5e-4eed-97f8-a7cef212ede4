#!/usr/bin/env node

/**
 * Quick Deployment Helper for Rushrr Courier App
 * Store: stoee3923u.myshopify.com
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const STORE_URL = 'stoee3923u.myshopify.com';
const SCOPES = 'read_orders,write_products,read_customers';

console.log('🚀 Rushrr Courier App - Quick Deployment Helper');
console.log('==============================================');
console.log(`📱 Target Store: ${STORE_URL}`);
console.log('');

async function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function updateConfiguration() {
  console.log('📋 Step 1: App Configuration');
  console.log('');
  
  const clientId = await askQuestion('Enter your Shopify App Client ID (from Partners Dashboard): ');
  const clientSecret = await askQuestion('Enter your Shopify App Client Secret: ');
  const tunnelUrl = await askQuestion('Enter your tunnel URL (e.g., https://abc123.ngrok.io): ');
  
  // Update .env file
  const envContent = `# Shopify App Configuration
SHOPIFY_API_KEY=${clientId}
SHOPIFY_API_SECRET=${clientSecret}
SHOPIFY_APP_URL=${tunnelUrl}
SCOPES=${SCOPES}

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=development
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_development_only

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=${tunnelUrl}

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

  writeFileSync('.env', envContent);
  console.log('✅ Updated .env file');

  // Update shopify.app.toml
  const tomlContent = `# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "${clientId}"
name = "rushrr-courier-app"
handle = "rushrr-courier-app"
application_url = "${tunnelUrl}"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "${SCOPES}"

[auth]
redirect_urls = ["${tunnelUrl}/auth/callback", "${tunnelUrl}/auth/shopify/callback", "${tunnelUrl}/api/auth/callback"]

[pos]
embedded = false
`;

  writeFileSync('shopify.app.toml', tomlContent);
  console.log('✅ Updated shopify.app.toml file');
  
  return { clientId, tunnelUrl };
}

function buildApp() {
  console.log('');
  console.log('🔨 Step 2: Building the app...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ App built successfully');
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

function startServer() {
  console.log('');
  console.log('🚀 Step 3: Starting the server...');
  console.log('💡 Make sure your tunnel is running in another terminal!');
  console.log('');
  
  try {
    execSync('npm start', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Server failed to start:', error.message);
  }
}

function generateInstallationURL(clientId, tunnelUrl) {
  const installUrl = `https://${STORE_URL}/admin/oauth/authorize?client_id=${clientId}&scope=${SCOPES}&redirect_uri=${encodeURIComponent(tunnelUrl + '/auth/callback')}`;
  
  console.log('');
  console.log('📱 INSTALLATION URL');
  console.log('==================');
  console.log('Copy and paste this URL in your browser to install the app:');
  console.log('');
  console.log(installUrl);
  console.log('');
  console.log('📋 Next Steps:');
  console.log('1. Make sure your tunnel is running');
  console.log('2. Make sure your server is running (npm start)');
  console.log('3. Open the installation URL above');
  console.log('4. Log in to your store with password: leotru');
  console.log('5. Authorize the app permissions');
  console.log('');
}

async function main() {
  try {
    const { clientId, tunnelUrl } = await updateConfiguration();
    buildApp();
    
    console.log('');
    console.log('⚠️  IMPORTANT: Before continuing, make sure you have:');
    console.log('1. Created your app in Shopify Partners Dashboard');
    console.log('2. Updated the app URLs in Partners Dashboard to match your tunnel URL');
    console.log('3. Started your tunnel (ngrok or cloudflare)');
    console.log('');
    
    const ready = await askQuestion('Are you ready to start the server? (y/n): ');
    
    if (ready.toLowerCase() === 'y' || ready.toLowerCase() === 'yes') {
      generateInstallationURL(clientId, tunnelUrl);
      
      const startNow = await askQuestion('Start the server now? (y/n): ');
      if (startNow.toLowerCase() === 'y' || startNow.toLowerCase() === 'yes') {
        rl.close();
        startServer();
      } else {
        console.log('');
        console.log('To start the server manually, run: npm start');
        rl.close();
      }
    } else {
      console.log('');
      console.log('Complete the setup steps above, then run this script again.');
      rl.close();
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    rl.close();
    process.exit(1);
  }
}

// Run the deployment helper
main();
