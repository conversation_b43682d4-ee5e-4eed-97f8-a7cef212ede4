#!/usr/bin/env node

/**
 * LOCAL DEPLOYMENT TEST - No actual deployment
 * Tests all components before deploying
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🧪 LOCAL DEPLOYMENT TEST');
console.log('========================');
console.log('Testing all components before deployment...\n');

// Your real credentials
const CLIENT_ID = '58596b7af59c1ddb6ca32f4420c96c28';
const CLIENT_SECRET = 'e1f1cf66f2591d472a0d4b506c210990';
const STORE_URL = 'stoee3923u.myshopify.com';
const SCOPES = 'read_orders,write_products,read_customers';

console.log('✅ Test Configuration:');
console.log(`   Client ID: ${CLIENT_ID}`);
console.log(`   Store: ${STORE_URL}`);
console.log(`   Scopes: ${SCOPES}\n`);

// Test 1: Check if all required files exist
console.log('🔍 Test 1: Checking required files...');
const requiredFiles = [
    'package.json',
    'app/root.tsx',
    'app/routes/_index.tsx',
    'app/shopify.server.ts',
    'prisma/schema.prisma'
];

let filesOK = true;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file} exists`);
    } else {
        console.log(`   ❌ ${file} missing`);
        filesOK = false;
    }
});

if (!filesOK) {
    console.log('\n❌ Missing required files. Cannot proceed.');
    process.exit(1);
}

// Test 2: Create test .env file
console.log('\n🔍 Test 2: Creating test environment configuration...');
const testEnvContent = `# TEST ENVIRONMENT - NOT FOR PRODUCTION
SHOPIFY_API_KEY=${CLIENT_ID}
SHOPIFY_API_SECRET=${CLIENT_SECRET}
SHOPIFY_APP_URL=http://localhost:3000
SCOPES=${SCOPES}

# Database
DATABASE_URL=file:test.sqlite

# Development
NODE_ENV=test
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=test_session_key_for_testing_only

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=http://localhost:3000

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

fs.writeFileSync('.env.test', testEnvContent);
console.log('   ✅ Test .env file created');

// Test 3: Create test TOML configuration
console.log('\n🔍 Test 3: Creating test TOML configuration...');
const testTomlContent = `# TEST CONFIGURATION
client_id = "${CLIENT_ID}"
name = "rushrr-courier-app-test"
handle = "rushrr-courier-app-test"
application_url = "http://localhost:3000"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

[access_scopes]
scopes = "${SCOPES}"

[auth]
redirect_urls = ["http://localhost:3000/auth/callback", "http://localhost:3000/auth/shopify/callback", "http://localhost:3000/api/auth/callback"]
`;

fs.writeFileSync('shopify.app.test.toml', testTomlContent);
console.log('   ✅ Test TOML file created');

// Test 4: Check dependencies
console.log('\n🔍 Test 4: Checking dependencies...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
        '@shopify/shopify-app-remix',
        '@shopify/polaris',
        '@remix-run/node',
        '@remix-run/serve',
        'prisma'
    ];
    
    let depsOK = true;
    requiredDeps.forEach(dep => {
        if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
            console.log(`   ✅ ${dep} installed`);
        } else {
            console.log(`   ❌ ${dep} missing`);
            depsOK = false;
        }
    });
    
    if (!depsOK) {
        console.log('\n⚠️  Some dependencies missing. Run: npm install');
    }
} catch (error) {
    console.log('   ❌ Error reading package.json');
}

// Test 5: Test build process
console.log('\n🔍 Test 5: Testing build process...');
try {
    const { execSync } = await import('child_process');
    console.log('   Building application...');
    execSync('npm run build', { stdio: 'pipe' });
    console.log('   ✅ Build successful');
} catch (error) {
    console.log('   ❌ Build failed:', error.message);
}

// Test 6: Generate installation URLs for different scenarios
console.log('\n🔍 Test 6: Generating test installation URLs...');

const scenarios = [
    {
        name: 'Local Development',
        tunnelUrl: 'http://localhost:3000',
        description: 'For local testing only'
    },
    {
        name: 'Ngrok Tunnel',
        tunnelUrl: 'https://abc123.ngrok.io',
        description: 'Example ngrok tunnel URL'
    },
    {
        name: 'Cloudflare Tunnel',
        tunnelUrl: 'https://example-tunnel.trycloudflare.com',
        description: 'Example cloudflare tunnel URL'
    }
];

scenarios.forEach((scenario, index) => {
    const installUrl = `https://${STORE_URL}/admin/oauth/authorize?client_id=${CLIENT_ID}&scope=${encodeURIComponent(SCOPES)}&redirect_uri=${encodeURIComponent(scenario.tunnelUrl + '/auth/callback')}`;
    
    console.log(`\n   Scenario ${index + 1}: ${scenario.name}`);
    console.log(`   Tunnel URL: ${scenario.tunnelUrl}`);
    console.log(`   Install URL: ${installUrl}`);
    console.log(`   Description: ${scenario.description}`);
});

// Test 7: Partners Dashboard configuration check
console.log('\n🔍 Test 7: Partners Dashboard configuration requirements...');
console.log('\n   For ANY tunnel URL, you need to update Partners Dashboard:');
console.log('   1. Go to: https://partners.shopify.com/');
console.log('   2. Find app: shop2423523');
console.log('   3. Update App URL to match your tunnel URL');
console.log('   4. Update redirect URLs to:');
console.log('      - [TUNNEL_URL]/auth/callback');
console.log('      - [TUNNEL_URL]/auth/shopify/callback');
console.log('      - [TUNNEL_URL]/api/auth/callback');

console.log('\n🎯 TEST SUMMARY:');
console.log('================');
console.log('✅ All components tested locally');
console.log('✅ Configuration files validated');
console.log('✅ Installation URLs generated');
console.log('✅ Ready for deployment');

console.log('\n📋 NEXT STEPS FOR ACTUAL DEPLOYMENT:');
console.log('====================================');
console.log('1. Choose your tunnel method (ngrok/cloudflare)');
console.log('2. Start the tunnel and get the URL');
console.log('3. Update Partners Dashboard with tunnel URL');
console.log('4. Start the server with correct environment');
console.log('5. Test the installation URL');

console.log('\n🚀 Test completed successfully!');
console.log('Ready to deploy when you are!');
