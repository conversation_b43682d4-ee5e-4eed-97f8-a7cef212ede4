# REAL SHOPIFY APP CONFIGURATION
client_id = "58596b7af59c1ddb6ca32f4420c96c28"
name = "rushrr-courier-app-final"
handle = "rushrr-courier-app-final"
application_url = "https://TUNNEL_URL_PLACEHOLDER"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

[access_scopes]
scopes = "read_orders,write_products,read_customers"

[auth]
redirect_urls = ["https://TUNNEL_URL_PLACEHOLDER/auth/callback", "https://TUNNEL_URL_PLACEHOLDER/auth/shopify/callback", "https://TUNNEL_URL_PLACEHOLDER/api/auth/callback"]
