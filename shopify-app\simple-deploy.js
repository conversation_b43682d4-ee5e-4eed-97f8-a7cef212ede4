#!/usr/bin/env node

/**
 * Simple Deployment Server for Rushrr Courier App
 * This creates a basic server that you can use to deploy your app
 */

import { execSync } from 'child_process';
import { writeFileSync } from 'fs';

const STORE_URL = 'stoee3923u.myshopify.com';
const SCOPES = 'read_orders,write_products,read_customers';

console.log('🚀 Rushrr Courier App - Simple Deployment');
console.log('=========================================');
console.log(`📱 Target Store: ${STORE_URL}`);
console.log('');

// Create a temporary app configuration for development
const tempClientId = 'temp_dev_app_' + Date.now();
const appUrl = 'http://localhost:3000';

console.log('📋 Setting up temporary development configuration...');

// Update .env file with temporary configuration
const envContent = `# Shopify App Configuration - Development
SHOPIFY_API_KEY=${tempClientId}
SHOPIFY_API_SECRET=temp_secret_for_development
SHOPIFY_APP_URL=${appUrl}
SCOPES=${SCOPES}

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=development
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_development_only

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=${appUrl}

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

writeFileSync('.env', envContent);
console.log('✅ Updated .env file');

// Update shopify.app.toml with temporary configuration
const tomlContent = `# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "${tempClientId}"
name = "rushrr-courier-app"
handle = "rushrr-courier-app"
application_url = "${appUrl}"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "${SCOPES}"

[auth]
redirect_urls = ["${appUrl}/auth/callback", "${appUrl}/auth/shopify/callback", "${appUrl}/api/auth/callback"]

[pos]
embedded = false
`;

writeFileSync('shopify.app.toml', tomlContent);
console.log('✅ Updated shopify.app.toml file');

console.log('');
console.log('🔨 Building the app...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ App built successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

console.log('');
console.log('🚀 Starting the development server...');
console.log('');
console.log('📋 IMPORTANT NEXT STEPS:');
console.log('========================');
console.log('');
console.log('1. Your app will start on http://localhost:3000');
console.log('2. To deploy to your Shopify store, you need to:');
console.log('');
console.log('   a) Create a Shopify Partners account at: https://partners.shopify.com/');
console.log('   b) Create a new app in the Partners Dashboard');
console.log('   c) Get your Client ID and Client Secret');
console.log('   d) Set up a tunnel (ngrok or cloudflare) for HTTPS access');
console.log('   e) Update your app configuration with real credentials');
console.log('');
console.log('3. For now, you can test the app locally at: http://localhost:3000');
console.log('');
console.log('🔗 Installation URL for your store (after proper setup):');
console.log(`https://${STORE_URL}/admin/oauth/authorize?client_id=YOUR_REAL_CLIENT_ID&scope=${SCOPES}&redirect_uri=https://your-tunnel-url.com/auth/callback`);
console.log('');
console.log('Starting server in 3 seconds...');

setTimeout(() => {
  try {
    execSync('npm start', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Server failed to start:', error.message);
    console.log('');
    console.log('💡 Try running manually: npm start');
  }
}, 3000);
