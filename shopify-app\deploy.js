#!/usr/bin/env node

/**
 * Shopify App Deployment Script
 * This script helps deploy the Rushrr Courier app to your Shopify store
 */

import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import path from 'path';

const STORE_URL = 'stoee3923u.myshopify.com';
const CLIENT_ID = 'YOUR_CLIENT_ID_HERE'; // Will be provided after app creation
const SCOPES = 'read_orders,write_products,read_customers';
const REDIRECT_URI = 'http://localhost:3000/auth/callback';

console.log('🚀 Rushrr Courier App Deployment Script');
console.log('=====================================');

// Check if server is running
function checkServer() {
  try {
    execSync('curl -s http://localhost:3000', { stdio: 'pipe' });
    console.log('✅ Server is running on localhost:3000');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Starting server...');
    return false;
  }
}

// Start the server
function startServer() {
  try {
    console.log('🔄 Starting the application server...');
    execSync('npm start', { 
      stdio: 'inherit',
      cwd: process.cwd(),
      detached: true
    });
    console.log('✅ Server started successfully');
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Generate installation URL
function generateInstallationURL() {
  const installUrl = `https://${STORE_URL}/admin/oauth/authorize?client_id=${CLIENT_ID}&scope=${SCOPES}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}`;
  
  console.log('\n📱 INSTALLATION INSTRUCTIONS');
  console.log('============================');
  console.log('1. Make sure your server is running (npm start)');
  console.log('2. Open the following URL in your browser:');
  console.log('\n🔗 Installation URL:');
  console.log(installUrl);
  console.log('\n3. Log in to your Shopify account when prompted');
  console.log('4. Authorize the app permissions');
  console.log('5. The app will be installed in your store!');
  
  return installUrl;
}

// Main deployment function
async function deploy() {
  console.log(`\n🎯 Deploying to: ${STORE_URL}`);
  console.log(`📋 Client ID: ${CLIENT_ID}`);
  console.log(`🔐 Scopes: ${SCOPES}`);
  
  // Check if server is running
  if (!checkServer()) {
    console.log('\n⚠️  Please start the server manually:');
    console.log('   npm start');
    console.log('\nThen run this script again or use the installation URL below.');
  }
  
  // Generate and display installation URL
  const installUrl = generateInstallationURL();
  
  console.log('\n✨ Deployment configuration complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. Ensure your server is running: npm start');
  console.log('2. Open the installation URL above');
  console.log('3. Complete the OAuth flow in your browser');
  
  return installUrl;
}

// Run deployment
if (import.meta.url === `file://${process.argv[1]}`) {
  deploy().catch(console.error);
}

export { deploy, generateInstallationURL };
