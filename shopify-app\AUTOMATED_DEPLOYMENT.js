#!/usr/bin/env node

/**
 * FULLY AUTOMATED DEPLOYMENT WITH PLAYWRIGHT
 * Automates everything: tunnel, Partners Dashboard, server, testing
 */

import { chromium } from 'playwright';
import { spawn, execSync } from 'child_process';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Your credentials
const CLIENT_ID = '58596b7af59c1ddb6ca32f4420c96c28';
const CLIENT_SECRET = 'e1f1cf66f2591d472a0d4b506c210990';
const STORE_URL = 'stoee3923u.myshopify.com';
const SCOPES = 'read_orders,write_products,read_customers';

console.log('🤖 FULLY AUTOMATED DEPLOYMENT');
console.log('==============================');
console.log('Automating EVERYTHING with Playwright...\n');

let tunnelUrl = '';
let tunnelProcess = null;
let serverProcess = null;

// Step 1: Start tunnel and get URL
async function startTunnel() {
    console.log('🚇 Step 1: Starting Cloudflare tunnel...');
    
    return new Promise((resolve, reject) => {
        tunnelProcess = spawn('npx', ['cloudflared', 'tunnel', '--url', 'http://localhost:3000'], {
            stdio: 'pipe'
        });
        
        let output = '';
        
        tunnelProcess.stdout.on('data', (data) => {
            output += data.toString();
            console.log('   Tunnel output:', data.toString().trim());
            
            // Look for tunnel URL
            const urlMatch = output.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
            if (urlMatch && !tunnelUrl) {
                tunnelUrl = urlMatch[0];
                console.log(`   ✅ Tunnel URL: ${tunnelUrl}`);
                resolve(tunnelUrl);
            }
        });
        
        tunnelProcess.stderr.on('data', (data) => {
            console.log('   Tunnel stderr:', data.toString().trim());
        });
        
        tunnelProcess.on('error', (error) => {
            console.log('   ❌ Tunnel error:', error.message);
            reject(error);
        });
        
        // Timeout after 30 seconds
        setTimeout(() => {
            if (!tunnelUrl) {
                reject(new Error('Tunnel startup timeout'));
            }
        }, 30000);
    });
}

// Step 2: Update configuration files
async function updateConfigFiles(tunnelUrl) {
    console.log('\n🔧 Step 2: Updating configuration files...');
    
    // Update .env
    const envContent = `# REAL SHOPIFY APP CREDENTIALS
SHOPIFY_API_KEY=${CLIENT_ID}
SHOPIFY_API_SECRET=${CLIENT_SECRET}
SHOPIFY_APP_URL=${tunnelUrl}
SCOPES=${SCOPES}

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=development
PORT=3000
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_development

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=${tunnelUrl}

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;
    
    fs.writeFileSync('.env', envContent);
    console.log('   ✅ .env updated');
    
    // Update TOML
    const tomlContent = `# REAL SHOPIFY APP CONFIGURATION
client_id = "${CLIENT_ID}"
name = "rushrr-courier-app-final"
handle = "rushrr-courier-app-final"
application_url = "${tunnelUrl}"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

[access_scopes]
scopes = "${SCOPES}"

[auth]
redirect_urls = ["${tunnelUrl}/auth/callback", "${tunnelUrl}/auth/shopify/callback", "${tunnelUrl}/api/auth/callback"]
`;
    
    fs.writeFileSync('shopify.app.toml', tomlContent);
    console.log('   ✅ shopify.app.toml updated');
}

// Step 3: Automate Partners Dashboard update
async function updatePartnersDashboard(tunnelUrl) {
    console.log('\n🌐 Step 3: Automating Partners Dashboard update...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // Slow down for visibility
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Navigate to Partners Dashboard
        console.log('   📱 Opening Partners Dashboard...');
        await page.goto('https://partners.shopify.com/');
        
        // Wait for login or dashboard
        await page.waitForTimeout(3000);
        
        // Check if we need to login
        const loginButton = await page.locator('text=Log in').first();
        if (await loginButton.isVisible()) {
            console.log('   🔐 Login required - please login manually...');
            console.log('   ⏳ Waiting for login completion...');
            
            // Wait for navigation after login
            await page.waitForURL('**/partners.shopify.com/**', { timeout: 120000 });
            console.log('   ✅ Login completed');
        }
        
        // Search for the app
        console.log('   🔍 Searching for app: shop2423523...');
        
        // Try different selectors for search
        const searchSelectors = [
            'input[placeholder*="Search"]',
            'input[type="search"]',
            '[data-testid="search-input"]',
            '.search-input',
            'input[name="search"]'
        ];
        
        let searchInput = null;
        for (const selector of searchSelectors) {
            try {
                searchInput = await page.locator(selector).first();
                if (await searchInput.isVisible()) {
                    break;
                }
            } catch (e) {
                continue;
            }
        }
        
        if (searchInput) {
            await searchInput.fill('shop2423523');
            await page.keyboard.press('Enter');
            await page.waitForTimeout(2000);
        }
        
        // Look for the app in the list
        console.log('   📱 Looking for app in the list...');
        const appLink = await page.locator('text=shop2423523').first();
        
        if (await appLink.isVisible()) {
            console.log('   ✅ Found app, clicking...');
            await appLink.click();
            await page.waitForTimeout(3000);
            
            // Look for app settings or configuration
            const settingsSelectors = [
                'text=App setup',
                'text=Configuration',
                'text=Settings',
                'text=App URL',
                '[data-testid="app-setup"]'
            ];
            
            for (const selector of settingsSelectors) {
                try {
                    const element = await page.locator(selector).first();
                    if (await element.isVisible()) {
                        await element.click();
                        await page.waitForTimeout(2000);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            // Update App URL
            console.log('   🔧 Updating App URL...');
            const appUrlInput = await page.locator('input[label*="App URL"], input[placeholder*="App URL"], input[name*="url"]').first();
            if (await appUrlInput.isVisible()) {
                await appUrlInput.clear();
                await appUrlInput.fill(tunnelUrl);
                console.log(`   ✅ App URL updated to: ${tunnelUrl}`);
            }
            
            // Update redirect URLs
            console.log('   🔧 Updating redirect URLs...');
            const redirectUrls = [
                `${tunnelUrl}/auth/callback`,
                `${tunnelUrl}/auth/shopify/callback`,
                `${tunnelUrl}/api/auth/callback`
            ];
            
            // Look for redirect URL inputs
            const redirectInputs = await page.locator('input[label*="redirect"], input[placeholder*="redirect"], textarea[label*="redirect"]').all();
            
            if (redirectInputs.length > 0) {
                const redirectInput = redirectInputs[0];
                await redirectInput.clear();
                await redirectInput.fill(redirectUrls.join('\n'));
                console.log('   ✅ Redirect URLs updated');
            }
            
            // Save changes
            console.log('   💾 Saving changes...');
            const saveButton = await page.locator('button:has-text("Save"), button:has-text("Update"), button[type="submit"]').first();
            if (await saveButton.isVisible()) {
                await saveButton.click();
                await page.waitForTimeout(3000);
                console.log('   ✅ Changes saved');
            }
            
        } else {
            console.log('   ⚠️  App not found automatically');
            console.log('   📋 Manual update required:');
            console.log(`      App URL: ${tunnelUrl}`);
            console.log('      Redirect URLs:');
            redirectUrls.forEach(url => console.log(`        - ${url}`));
        }
        
    } catch (error) {
        console.log('   ⚠️  Automated update failed:', error.message);
        console.log('   📋 Manual update required:');
        console.log(`      App URL: ${tunnelUrl}`);
        console.log('      Redirect URLs:');
        console.log(`        - ${tunnelUrl}/auth/callback`);
        console.log(`        - ${tunnelUrl}/auth/shopify/callback`);
        console.log(`        - ${tunnelUrl}/api/auth/callback`);
    }
    
    // Keep browser open for manual verification
    console.log('   🔍 Browser kept open for verification...');
    console.log('   ⏳ Press Enter when Partners Dashboard is updated...');
    
    // Wait for user confirmation
    await new Promise(resolve => {
        process.stdin.once('data', () => resolve());
    });
    
    await browser.close();
}

// Step 4: Start server
async function startServer(tunnelUrl) {
    console.log('\n🚀 Step 4: Starting server...');
    
    return new Promise((resolve) => {
        const env = {
            ...process.env,
            SHOPIFY_API_KEY: CLIENT_ID,
            SHOPIFY_API_SECRET: CLIENT_SECRET,
            SHOPIFY_APP_URL: tunnelUrl,
            SCOPES: SCOPES,
            NODE_ENV: 'development',
            PORT: '3000'
        };
        
        serverProcess = spawn('npm', ['start'], {
            stdio: 'pipe',
            env: env
        });
        
        serverProcess.stdout.on('data', (data) => {
            console.log('   Server:', data.toString().trim());
            
            if (data.toString().includes('localhost:3000') || data.toString().includes('Server running')) {
                console.log('   ✅ Server started successfully');
                resolve();
            }
        });
        
        serverProcess.stderr.on('data', (data) => {
            console.log('   Server stderr:', data.toString().trim());
        });
        
        // Resolve after 10 seconds regardless
        setTimeout(resolve, 10000);
    });
}

// Step 5: Test installation
async function testInstallation(tunnelUrl) {
    console.log('\n🧪 Step 5: Testing installation...');
    
    const installUrl = `https://${STORE_URL}/admin/oauth/authorize?client_id=${CLIENT_ID}&scope=${encodeURIComponent(SCOPES)}&redirect_uri=${encodeURIComponent(tunnelUrl + '/auth/callback')}`;
    
    console.log(`   🔗 Installation URL: ${installUrl}`);
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        console.log('   🌐 Opening installation URL...');
        await page.goto(installUrl);
        
        // Wait for page to load
        await page.waitForTimeout(5000);
        
        // Check for success or error
        const pageContent = await page.content();
        
        if (pageContent.includes('redirect_uri is not whitelisted')) {
            console.log('   ❌ Redirect URI still not whitelisted');
            console.log('   📋 Please verify Partners Dashboard settings');
        } else if (pageContent.includes('Install') || pageContent.includes('authorize')) {
            console.log('   ✅ Installation page loaded successfully!');
            console.log('   🎉 Redirect URI issue FIXED!');
        } else {
            console.log('   ⚠️  Unexpected page content');
        }
        
        console.log('   🔍 Browser kept open for testing...');
        console.log('   ⏳ Press Enter to continue...');
        
        await new Promise(resolve => {
            process.stdin.once('data', () => resolve());
        });
        
    } catch (error) {
        console.log('   ❌ Test failed:', error.message);
    }
    
    await browser.close();
}

// Cleanup function
function cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (tunnelProcess) {
        tunnelProcess.kill();
        console.log('   ✅ Tunnel stopped');
    }
    
    if (serverProcess) {
        serverProcess.kill();
        console.log('   ✅ Server stopped');
    }
}

// Main execution
async function main() {
    try {
        // Install Playwright if needed
        try {
            execSync('npx playwright install chromium', { stdio: 'pipe' });
        } catch (e) {
            console.log('   ⚠️  Playwright install failed, continuing...');
        }
        
        // Execute all steps
        const tunnelUrl = await startTunnel();
        await updateConfigFiles(tunnelUrl);
        await updatePartnersDashboard(tunnelUrl);
        await startServer(tunnelUrl);
        await testInstallation(tunnelUrl);
        
        console.log('\n🎉 AUTOMATED DEPLOYMENT COMPLETE!');
        console.log('==================================');
        console.log('✅ Tunnel started');
        console.log('✅ Configuration updated');
        console.log('✅ Partners Dashboard updated');
        console.log('✅ Server started');
        console.log('✅ Installation tested');
        console.log('\n🚀 Your app is ready!');
        
    } catch (error) {
        console.log('\n❌ Deployment failed:', error.message);
    } finally {
        cleanup();
    }
}

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Run the automation
main().catch(console.error);
