#!/usr/bin/env node

/**
 * Fully Automated Deployment for Rushrr Courier App
 * This script handles everything automatically
 */

import { execSync, spawn } from 'child_process';
import { writeFileSync, readFileSync } from 'fs';
import { setTimeout } from 'timers/promises';

const STORE_URL = 'stoee3923u.myshopify.com';
const SCOPES = 'read_orders,write_products,read_customers';
const APP_PORT = 54713;

console.log('🚀 AUTOMATED DEPLOYMENT STARTING...');
console.log('=====================================');
console.log(`📱 Target Store: ${STORE_URL}`);
console.log(`🖥️  App Port: ${APP_PORT}`);
console.log('');

async function startTunnel() {
  console.log('🌐 Step 1: Starting Cloudflare Tunnel...');
  
  return new Promise((resolve, reject) => {
    const tunnel = spawn('npx', ['cloudflared', 'tunnel', '--url', `http://localhost:${APP_PORT}`], {
      stdio: 'pipe'
    });
    
    let tunnelUrl = '';
    
    tunnel.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Tunnel output:', output);
      
      // Look for the tunnel URL
      const urlMatch = output.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
      if (urlMatch && !tunnelUrl) {
        tunnelUrl = urlMatch[0];
        console.log(`✅ Tunnel URL: ${tunnelUrl}`);
        resolve({ tunnelUrl, process: tunnel });
      }
    });
    
    tunnel.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('Tunnel stderr:', output);
      
      // Also check stderr for URL
      const urlMatch = output.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
      if (urlMatch && !tunnelUrl) {
        tunnelUrl = urlMatch[0];
        console.log(`✅ Tunnel URL: ${tunnelUrl}`);
        resolve({ tunnelUrl, process: tunnel });
      }
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!tunnelUrl) {
        tunnel.kill();
        reject(new Error('Tunnel setup timeout'));
      }
    }, 30000);
  });
}

function updateConfiguration(tunnelUrl) {
  console.log('⚙️  Step 2: Updating Configuration...');
  
  // Generate a temporary client ID for development
  const tempClientId = 'auto_deploy_' + Date.now();
  
  // Update .env file
  const envContent = `# Shopify App Configuration - Auto Deployed
SHOPIFY_API_KEY=${tempClientId}
SHOPIFY_API_SECRET=auto_deploy_secret_${Date.now()}
SHOPIFY_APP_URL=${tunnelUrl}
SCOPES=${SCOPES}

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=production
PORT=${APP_PORT}
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=auto_deploy_session_${Date.now()}

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=${tunnelUrl}

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
`;

  writeFileSync('.env', envContent);
  console.log('✅ Updated .env file');

  // Update shopify.app.toml
  const tomlContent = `# Auto-deployed configuration
client_id = "${tempClientId}"
name = "rushrr-courier-app-auto"
handle = "rushrr-courier-app-auto"
application_url = "${tunnelUrl}"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
scopes = "${SCOPES}"

[auth]
redirect_urls = ["${tunnelUrl}/auth/callback", "${tunnelUrl}/auth/shopify/callback", "${tunnelUrl}/api/auth/callback"]

[pos]
embedded = false
`;

  writeFileSync('shopify.app.toml', tomlContent);
  console.log('✅ Updated shopify.app.toml file');
  
  return tempClientId;
}

function buildAndStartApp() {
  console.log('🔨 Step 3: Building and Starting App...');
  
  try {
    console.log('Building...');
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ App built successfully');
    
    console.log('Starting server...');
    const server = spawn('npm', ['start'], {
      stdio: 'pipe',
      detached: true
    });
    
    server.stdout.on('data', (data) => {
      console.log('Server:', data.toString());
    });
    
    return server;
  } catch (error) {
    console.error('❌ Build/Start failed:', error.message);
    throw error;
  }
}

function generateInstallationInfo(tunnelUrl, clientId) {
  console.log('');
  console.log('🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!');
  console.log('====================================');
  console.log('');
  console.log('📱 Your App URLs:');
  console.log(`   • Public URL: ${tunnelUrl}`);
  console.log(`   • Local URL:  http://localhost:${APP_PORT}`);
  console.log('');
  console.log('🏪 Store Information:');
  console.log(`   • Store: ${STORE_URL}`);
  console.log('   • Password: leotru');
  console.log('');
  console.log('🔗 To install on your store:');
  console.log('   1. Go to Shopify Partners Dashboard: https://partners.shopify.com/');
  console.log('   2. Create a new app with these settings:');
  console.log(`      - App URL: ${tunnelUrl}`);
  console.log(`      - Redirect URLs: ${tunnelUrl}/auth/callback`);
  console.log(`      - Scopes: ${SCOPES}`);
  console.log('   3. Get your real Client ID and update the configuration');
  console.log('');
  console.log('🚀 Quick Install URL (after getting real Client ID):');
  console.log(`https://${STORE_URL}/admin/oauth/authorize?client_id=YOUR_REAL_CLIENT_ID&scope=${SCOPES}&redirect_uri=${encodeURIComponent(tunnelUrl + '/auth/callback')}`);
  console.log('');
  console.log('✅ Your app is now publicly accessible and ready for Shopify!');
}

async function main() {
  try {
    // Step 1: Start tunnel
    const { tunnelUrl, process: tunnelProcess } = await startTunnel();
    
    // Step 2: Update configuration
    const clientId = updateConfiguration(tunnelUrl);
    
    // Step 3: Build and start app
    const serverProcess = buildAndStartApp();
    
    // Wait a moment for server to start
    await setTimeout(3000);
    
    // Step 4: Generate installation info
    generateInstallationInfo(tunnelUrl, clientId);
    
    // Keep processes running
    console.log('');
    console.log('🔄 Keeping tunnel and server running...');
    console.log('   Press Ctrl+C to stop');
    
    // Handle cleanup
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down...');
      tunnelProcess.kill();
      serverProcess.kill();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

// Start the automated deployment
main();
