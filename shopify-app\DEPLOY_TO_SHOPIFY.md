# 🚀 Deploy Rushrr Courier App to Shopify Store

## ✅ Current Status
Your app is **RUNNING LOCALLY** at: http://localhost:54713

## 🎯 Deploy to Your Shopify Store: stoee3923u.myshopify.com

### Step 1: Create Public Tunnel (REQUIRED)

**Option A: Using ngrok (Recommended)**
1. Open a **NEW terminal/command prompt**
2. Run: `ngrok http 54713`
3. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)

**Option B: Using Cloudflare Tunnel**
1. Open a **NEW terminal/command prompt**  
2. Run: `npx cloudflared tunnel --url http://localhost:54713`
3. Copy the HTTPS URL (e.g., `https://abc-def-ghi.trycloudflare.com`)

### Step 2: Create Shopify App

1. **Go to Shopify Partners Dashboard**: https://partners.shopify.com/
2. **Log in** or create account if needed
3. **Create New App**:
   - Click "Apps" → "Create app" → "Create app manually"
   - **App name**: `Rushrr Courier App`
   - **App URL**: `YOUR_TUNNEL_URL_FROM_STEP_1`
   - **Allowed redirection URLs**:
     ```
     YOUR_TUNNEL_URL/auth/callback
     YOUR_TUNNEL_URL/auth/shopify/callback
     YOUR_TUNNEL_URL/api/auth/callback
     ```

4. **Configure App Settings**:
   - **Embedded app**: ✅ Yes
   - **App scopes**: 
     - `read_orders`
     - `write_products`
     - `read_customers`

5. **Save and get credentials**:
   - Copy **Client ID** (API key)
   - Copy **Client Secret** (API secret key)

### Step 3: Update App Configuration

1. **Stop your current server** (Ctrl+C in the terminal running the app)

2. **Update .env file** with your real credentials:
```env
SHOPIFY_API_KEY=YOUR_CLIENT_ID_HERE
SHOPIFY_API_SECRET=YOUR_CLIENT_SECRET_HERE
SHOPIFY_APP_URL=YOUR_TUNNEL_URL_HERE
SCOPES=read_orders,write_products,read_customers

# Database
DATABASE_URL=file:dev.sqlite

# Development
NODE_ENV=production
PORT=54713
HOST=localhost

# External API Configuration
RUSHRR_API_BASE_URL=https://backend.rushr-admin.com/api

# Session Configuration
SESSION_SECRET=super_secret_session_key_for_production

# Logging
LOG_LEVEL=info

# Security
CORS_ORIGIN=YOUR_TUNNEL_URL_HERE

SHOPIFY_ADMIN_ACTION_123_ID=c14fae1d-fd17-4b78-9703-7602722028fa
SHOPIFY_LOGISTIC_ACTION_ID=0b51054d-497d-411b-b7d4-ce42c5467eaf
```

3. **Update shopify.app.toml**:
```toml
client_id = "YOUR_CLIENT_ID_HERE"
name = "rushrr-courier-app"
handle = "rushrr-courier-app"
application_url = "YOUR_TUNNEL_URL_HERE"
embedded = true

[access_scopes]
scopes = "read_orders,write_products,read_customers"

[auth]
redirect_urls = ["YOUR_TUNNEL_URL_HERE/auth/callback", "YOUR_TUNNEL_URL_HERE/auth/shopify/callback", "YOUR_TUNNEL_URL_HERE/api/auth/callback"]
```

### Step 4: Restart Your App

1. **Build the app**: `npm run build`
2. **Start the server**: `npm start`
3. **Keep your tunnel running** in the other terminal

### Step 5: Install App on Your Store

1. **Generate Installation URL**:
```
https://stoee3923u.myshopify.com/admin/oauth/authorize?client_id=YOUR_CLIENT_ID&scope=read_orders,write_products,read_customers&redirect_uri=YOUR_TUNNEL_URL/auth/callback
```

2. **Install the App**:
   - Open the installation URL in your browser
   - Log in to your store with password: `leotru`
   - Click "Install app" to authorize permissions
   - You'll be redirected to your app!

### Step 6: Access Your App

After installation, access your app:
- **From Shopify Admin**: https://stoee3923u.myshopify.com/admin/apps
- **Direct URL**: YOUR_TUNNEL_URL

## 🔧 Quick Commands

**Start everything in the right order:**

1. **Terminal 1** (App Server):
```bash
cd shopify-app
npm start
```

2. **Terminal 2** (Tunnel):
```bash
ngrok http 54713
# OR
npx cloudflared tunnel --url http://localhost:54713
```

## 🚨 Important Notes

- **Keep both terminals running** while using the app
- **HTTPS tunnel is required** - Shopify won't work with HTTP
- **Update Partners Dashboard** if your tunnel URL changes
- **Store password**: `leotru`

## 🆘 Troubleshooting

### "App not loading" 
- Check tunnel is running and accessible
- Verify app server is running on port 54713
- Ensure URLs in Partners Dashboard match your tunnel URL

### "Redirect URI not whitelisted"
- Update redirect URLs in Partners Dashboard
- Make sure URLs match exactly (including https://)

### "Database errors"
```bash
npx prisma generate
npx prisma db push
```

---

**Your app is ready! Follow these steps to deploy to your Shopify store.** 🎉

**Need help?** The app is currently running at http://localhost:54713 - just need to make it public and connect to Shopify!
