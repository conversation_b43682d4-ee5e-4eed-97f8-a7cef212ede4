@echo off
echo 🚀 COMPLETE SHOPIFY APP DEPLOYMENT
echo ==================================
echo.

echo Step 1: Building the application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)
echo ✅ Build successful
echo.

echo Step 2: Instructions for deployment:
echo.
echo 1. Open a NEW terminal and run: start-tunnel.bat
echo 2. Copy the tunnel URL (e.g., https://abc123.trycloudflare.com)
echo 3. Update Partners Dashboard with the tunnel URL:
echo    - Go to: https://partners.shopify.com/
echo    - Find app: shop2423523
echo    - Update App URL to: [TUNNEL_URL]
echo    - Update redirect URLs to:
echo      * [TUNNEL_URL]/auth/callback
echo      * [TUNNEL_URL]/auth/shopify/callback
echo      * [TUNNEL_URL]/api/auth/callback
echo 4. Run: start-server.bat [TUNNEL_URL]
echo 5. Test installation: https://stoee3923u.myshopify.com/admin/oauth/authorize?client_id=58596b7af59c1ddb6ca32f4420c96c28&scope=read_orders,write_products,read_customers&redirect_uri=[TUNNEL_URL]/auth/callback
echo.
echo ⚠️  Replace [TUNNEL_URL] with your actual tunnel URL!
echo.
pause
